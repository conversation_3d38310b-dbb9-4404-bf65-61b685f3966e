# puts "Hello, Ruby World!"
sample_hash = {a: 1, b: 2, c: 3, d: 4}

# p sample_hash

# sample_hash.each do |key, value|
#   p "sample hash has this #{key} key and this #{value} value"
  
# end

# sample_hash.each { |k, v| sample_hash.delete(k) if v > 3}
# puts sample_hash

userData = [
  {userName: "yogesh", password: "Pass"},
  {userName: "sourabh", password: "Pass1"}
]

attempts = 1

while attempts < 4 
  print "username: "
  username = gets.chomp
print "password: "
password = gets.chomp

userData.each do |user|
 if user[:userName] == username && user[:password] == password
  puts user
  break
 else
  puts "invalid credentials"
end
end

puts "press n to quite or any key to continue"
input = gets.chomp.downcase

break if input == "n"
attempts += 1
end
